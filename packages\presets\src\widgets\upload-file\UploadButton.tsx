import React, { useRef } from "react";

import { PresetsCommand } from "@/command";
import { useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const UploadButton: React.FC = () => {
  const runner = useCommandRunner();
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const files = e.target.files;
    runner(PresetsCommand.UploadFiles, { files });
    // 清空 input
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.value = "";
      }
    });
  };

  return (
    <div className="pts:cursor-pointer">
      <input type="file" onChange={handleChange} ref={inputRef} />
      <Icon icon="Attachment" />
    </div>
  );
};

export default UploadButton;
