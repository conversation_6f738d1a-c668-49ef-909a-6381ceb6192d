import React, { useCallback, useRef, useState } from "react";

import { PresetsCommand } from "@/command";
import { post, useSubscribeCommand } from "@cscs-agent/core";
import { nanoid } from "nanoid";

interface FileItem {
  id: string;
  name: string;
  type: "word" | "markdown" | "text" | "json";
  size: string;
  status: "success" | "failed" | "uploading";
  progress?: number; // Upload progress percentage (0-100)
}

const fileTypeIcons = {
  word: { icon: "W", bgColor: "bg-blue-500", textColor: "text-white" },
  markdown: { icon: "</>", bgColor: "bg-cyan-500", textColor: "text-white" },
  text: { icon: "≡", bgColor: "bg-gray-500", textColor: "text-white" },
  json: { icon: "</>", bgColor: "bg-cyan-500", textColor: "text-white" },
};

const FileList: React.FC = () => {
  const [files, setFiles] = useState<FileItem[]>([
    {
      id: "1",
      name: "Word文件名称新...",
      type: "word",
      size: "108 KB",
      status: "success",
    },
    {
      id: "2",
      name: "Markdown文件新...",
      type: "markdown",
      size: "md",
      status: "failed",
    },
    {
      id: "3",
      name: "上传中的文件...",
      type: "text",
      size: "256 KB",
      status: "uploading",
      progress: 45,
    },
    {
      id: "4",
      name: "Markdown文件新...",
      type: "markdown",
      size: "md",
      status: "failed",
    },
    {
      id: "5",
      name: "上传中的文件...",
      type: "text",
      size: "256 KB",
      status: "uploading",
      progress: 45,
    },
  ]);
  const innerRef = useRef<HTMLDivElement | null>(null);
  const wrapperRef = useRef<HTMLDivElement | null>(null);
  const [posX, setPosX] = useState(0);

  // Track file uploads to map File objects to FileItem IDs
  const fileUploadMapRef = useRef<Map<File, string>>(new Map());

  useSubscribeCommand(PresetsCommand.UploadFiles, ({ files }) => {
    uploadFiles(files);
  });

  // Helper function to determine file type based on file extension
  const getFileType = (fileName: string): FileItem["type"] => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "doc":
      case "docx":
        return "word";
      case "md":
        return "markdown";
      case "json":
        return "json";
      default:
        return "text";
    }
  };

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  // Enhanced onUploadProgress method
  const onUploadProgress = useCallback((file: File) => {
    return (progressEvent: { loaded: number; total?: number }) => {
      const { loaded, total } = progressEvent;
      const fileId = fileUploadMapRef.current.get(file);

      if (!fileId || !total) return;

      // Calculate progress percentage
      const progressPercentage = Math.round((loaded / total) * 100);

      // Update file progress in state
      setFiles((prevFiles) =>
        prevFiles.map((fileItem) =>
          fileItem.id === fileId ? { ...fileItem, progress: progressPercentage } : fileItem,
        ),
      );

      // Handle completion
      if (progressPercentage >= 100) {
        // Small delay to show 100% before changing to success
        setTimeout(() => {
          setFiles((prevFiles) =>
            prevFiles.map((fileItem) =>
              fileItem.id === fileId ? { ...fileItem, status: "success" as const, progress: undefined } : fileItem,
            ),
          );
          // Clean up the file tracking
          fileUploadMapRef.current.delete(file);
        }, 200);
      }
    };
  }, []);

  const uploadFiles = async (files: File[]) => {
    for (const file of files) {
      // Generate unique ID for this file
      const fileId = nanoid();

      // Create FileItem for this upload
      const newFileItem: FileItem = {
        id: fileId,
        name: file.name,
        type: getFileType(file.name),
        size: formatFileSize(file.size),
        status: "uploading",
        progress: 0,
      };

      // Add file to state
      setFiles((prevFiles) => [...prevFiles, newFileItem]);

      // Track the file upload
      fileUploadMapRef.current.set(file, fileId);

      // Prepare form data
      const formData = new FormData();
      formData.append("file", file);

      try {
        // Start upload with progress tracking
        await post("/rag/files/upload", formData, {
          onUploadProgress: onUploadProgress(file),
          timeout: 60000, // 60 second timeout for file uploads
        });
      } catch (error) {
        // Handle upload error
        console.error("Upload failed:", error);
        setFiles((prevFiles) =>
          prevFiles.map((fileItem) =>
            fileItem.id === fileId ? { ...fileItem, status: "failed" as const, progress: undefined } : fileItem,
          ),
        );
        // Clean up the file tracking
        fileUploadMapRef.current.delete(file);
      }
    }
  };

  const removeFile = (id: string) => {
    setFiles(files.filter((file) => file.id !== id));
  };

  const retryUpload = (id: string) => {
    // Find the original file item to get its details
    const fileItem = files.find((f) => f.id === id);
    if (!fileItem) return;

    // Reset the file status to uploading
    setFiles((prevFiles) =>
      prevFiles.map((file) => (file.id === id ? { ...file, status: "uploading" as const, progress: 0 } : file)),
    );

    // Note: In a real implementation, you would need to store the original File object
    // to retry the upload. For now, this just resets the status.
    // You might want to implement a more sophisticated retry mechanism
    // that stores the original File objects for retry purposes.
  };

  const swipeList = (direction: "left" | "right") => {
    const step = 204;
    const innerEle = innerRef.current;
    const wrapperEle = innerRef.current;
    const innerWidth = innerEle?.scrollWidth ?? 0;
    const wrapperWidth = wrapperEle?.getBoundingClientRect().width ?? 0;
    let newPosX = direction === "left" ? posX + step : posX - step;

    if (newPosX < wrapperWidth - innerWidth) {
      newPosX = wrapperWidth - innerWidth;
    }

    if (newPosX > 0) {
      newPosX = 0;
    }

    if (innerEle) {
      innerEle.style.transform = `translateX(${newPosX}px)`;
    }

    setPosX(newPosX);
  };

  return (
    <div className="pts:relative pts:w-full pts:max-w-4xl pts:overflow-hidden">
      {/* File Upload Container */}
      <div className="pts:bg-white pts:p-2">
        <div className="pts:flex pts:justify-between pts:items-center">
          <button
            onClick={() => swipeList("left")}
            className="pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:border-0 pts:rounded-full pts:w-6 pts:h-6 pts:transition-colors pts:cursor-pointer"
          >
            <svg className="pts:w-4 pts:h-4 pts:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          {/* File Items */}
          <div className="pts:gap-4 pts:overflow-hidden" ref={wrapperRef}>
            <div className="pts:whitespace-nowrap pts:transition-all pts:translate-x-0" ref={innerRef}>
              {files.map((file) => {
                const iconConfig = fileTypeIcons[file.type];

                return (
                  <div
                    key={file.id}
                    className="pts:inline-block pts:relative pts:bg-gray-50 pts:hover:bg-gray-100 pts:mr-2 pts:p-4 pts:rounded-lg pts:w-[196px] pts:transition-colors pts:cursor-pointer"
                    onClick={() => (file.status === "failed" ? retryUpload(file.id) : undefined)}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(file.id);
                      }}
                      className="pts:top-2 pts:right-2 pts:absolute pts:flex pts:justify-center pts:items-center pts:bg-gray-400 pts:hover:bg-gray-500 pts:border-0 pts:rounded-full pts:w-5 pts:h-5 pts:transition-colors pts:cursor-pointer"
                    >
                      <svg
                        className="pts:w-3 pts:h-3 pts:text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>

                    {/* File Icon and Info */}
                    <div className="pts:flex pts:items-center pts:gap-3 pts:mb-2">
                      <div
                        className={`pts:w-10 pts:h-10 ${iconConfig.bgColor} pts:rounded-lg pts:flex pts:items-center pts:justify-center`}
                      >
                        <span className={`pts:font-bold pts:text-sm ${iconConfig.textColor}`}>{iconConfig.icon}</span>
                      </div>
                      <div className="pts:flex-1 pts:min-w-0">
                        <div className="pts:font-medium pts:text-gray-900 pts:text-sm pts:truncate">{file.name}</div>
                        <div className="pts:flex pts:items-center pts:gap-2 pts:mt-1">
                          {file.status === "uploading" ? (
                            <span className="pts:font-medium pts:text-blue-500 pts:text-xs">
                              上传中 ... {Math.round(file.progress || 0)}%
                            </span>
                          ) : (
                            <span className="pts:text-gray-500 pts:text-xs">
                              {file.type === "markdown" ? "md" : file.size}
                            </span>
                          )}
                          {file.status === "failed" && (
                            <span className="pts:font-medium pts:text-red-500 pts:text-xs">上传失败</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Upload Progress Bar */}
                    {file.status === "uploading" && (
                      <div className="pts:bg-gray-200 pts:mb-2 pts:rounded-full pts:w-full pts:h-1.5">
                        <div
                          className="pts:bg-blue-500 pts:rounded-full pts:h-1.5 pts:transition-all pts:duration-300 pts:ease-out"
                          style={{ width: `${file.progress || 0}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          <button
            onClick={() => swipeList("right")}
            className="pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:border-0 pts:rounded-full pts:w-6 pts:h-6 pts:transition-colors pts:cursor-pointer"
          >
            <svg className="pts:w-4 pts:h-4 pts:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default FileList;
